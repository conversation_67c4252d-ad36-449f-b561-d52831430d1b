
Microsoft Visual Studio Solution File, Format Version 11.00
# Visual Studio 2010
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.XR.Interaction.Toolkit.Samples.ARStarterAssets", "Unity.XR.Interaction.Toolkit.Samples.ARStarterAssets.csproj", "{2a2cdfa8-073c-597a-fcdb-43ab73c5c7e6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.XR.Interaction.Toolkit.Samples.StarterAssets", "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj", "{f23f3120-10a8-0a48-6f4c-7b485e460d68}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MRTemplate", "MRTemplate.csproj", "{dc80481f-1428-6c6c-b338-2641f4500271}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.XR.Interaction.Toolkit.Samples.Hands", "Unity.XR.Interaction.Toolkit.Samples.Hands.csproj", "{16c80bf4-7fca-93c3-5cf4-f01632fa1ff3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.XR.Interaction.Toolkit.Samples.ARStarterAssets.Editor", "Unity.XR.Interaction.Toolkit.Samples.ARStarterAssets.Editor.csproj", "{2986bf40-b9bf-405b-55a3-eba36b83a992}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Assembly-CSharp", "Assembly-CSharp.csproj", "{9e396ce0-7456-a79b-dee3-cbe2d8ebc7fb}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.XR.Hands.Samples.VisualizerSample", "Unity.XR.Hands.Samples.VisualizerSample.csproj", "{603a924a-33d0-0659-3b09-2015638f8919}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.XR.Interaction.Toolkit.Samples.Hands.Editor", "Unity.XR.Interaction.Toolkit.Samples.Hands.Editor.csproj", "{0cb6277f-4a3f-68b2-a491-d3a820b44298}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor", "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj", "{540278ef-bcef-fcc6-263c-ad109011db88}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{2a2cdfa8-073c-597a-fcdb-43ab73c5c7e6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2a2cdfa8-073c-597a-fcdb-43ab73c5c7e6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{f23f3120-10a8-0a48-6f4c-7b485e460d68}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{f23f3120-10a8-0a48-6f4c-7b485e460d68}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{dc80481f-1428-6c6c-b338-2641f4500271}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{dc80481f-1428-6c6c-b338-2641f4500271}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{16c80bf4-7fca-93c3-5cf4-f01632fa1ff3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{16c80bf4-7fca-93c3-5cf4-f01632fa1ff3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2986bf40-b9bf-405b-55a3-eba36b83a992}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2986bf40-b9bf-405b-55a3-eba36b83a992}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9e396ce0-7456-a79b-dee3-cbe2d8ebc7fb}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9e396ce0-7456-a79b-dee3-cbe2d8ebc7fb}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{603a924a-33d0-0659-3b09-2015638f8919}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{603a924a-33d0-0659-3b09-2015638f8919}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0cb6277f-4a3f-68b2-a491-d3a820b44298}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0cb6277f-4a3f-68b2-a491-d3a820b44298}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{540278ef-bcef-fcc6-263c-ad109011db88}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{540278ef-bcef-fcc6-263c-ad109011db88}.Debug|Any CPU.Build.0 = Debug|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
