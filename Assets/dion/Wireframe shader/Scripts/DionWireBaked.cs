using System.Collections.Generic;
using UnityEngine;

namespace dion.MinaPecheux_Shaders_CrossPlatformWireframe.Scripts
{
    public class DionWireBaked : MonoBehaviour
    {
        private static Color[] _COLORS = new Color[]
        {
            Color.red,
            Color.green,
            Color.blue,
        };

        private SkinnedMeshRenderer _skinnedMeshRenderer;
        private Mesh _bakedMesh;

#if UNITY_EDITOR
        private void OnValidate()
        {
            // (called whenever the object is updated)
            UpdateMesh();
        }
#endif

        private void Awake()
        {
            _skinnedMeshRenderer = GetComponent<SkinnedMeshRenderer>();
            if (_skinnedMeshRenderer == null)
            {
                Debug.LogError("No SkinnedMeshRenderer found on this GameObject");
                return;
            }
            
            // Create a new mesh to store the baked result
            _bakedMesh = new Mesh();
            UpdateMesh();
        }

        [ContextMenu("Update Mesh")]
        public void UpdateMesh()
        {
            if (!gameObject.activeSelf)
                return;

            _skinnedMeshRenderer = _skinnedMeshRenderer ?? GetComponent<SkinnedMeshRenderer>();
            if (_skinnedMeshRenderer == null || !_skinnedMeshRenderer.enabled)
                return;

            // Bake the skinned mesh into a regular mesh
            _bakedMesh = new Mesh();
            _skinnedMeshRenderer.BakeMesh(_bakedMesh);

            // Compute and store vertex colors for the wireframe shader
            Color[] colors = _SortedColoring(_bakedMesh);

            if (colors != null)
            {
                _bakedMesh.SetColors(colors);
                _skinnedMeshRenderer.sharedMesh = _bakedMesh;
            }
        }

        private Color[] _SortedColoring(Mesh mesh)
        {
            int n = mesh.vertexCount;
            int[] labels = new int[n];

            List<int[]> triangles = _GetSortedTriangles(mesh.triangles);
            triangles.Sort((int[] t1, int[] t2) =>
            {
                int i = 0;
                while (i < t1.Length && i < t2.Length)
                {
                    if (t1[i] < t2[i]) return -1;
                    if (t1[i] > t2[i]) return 1;
                    i += 1;
                }
                if (t1.Length < t2.Length) return -1;
                if (t1.Length > t2.Length) return 1;
                return 0;
            });

            foreach (int[] triangle in triangles)
            {
                List<int> availableLabels = new List<int>() { 1, 2, 3 };
                foreach (int vertexIndex in triangle)
                {
                    if (availableLabels.Contains(labels[vertexIndex]))
                        availableLabels.Remove(labels[vertexIndex]);
                }
                foreach (int vertexIndex in triangle)
                {
                    if (labels[vertexIndex] == 0)
                    {
                        if (availableLabels.Count == 0)
                        {
                            Debug.LogError("Could not find color");
                            return null;
                        }
                        labels[vertexIndex] = availableLabels[0];
                        availableLabels.RemoveAt(0);
                    }
                }
            }

            Color[] colors = new Color[n];
            for (int i = 0; i < n; i++)
                colors[i] = labels[i] > 0 ? _COLORS[labels[i] - 1] : _COLORS[0];

            return colors;
        }

        private List<int[]> _GetSortedTriangles(int[] triangles)
        {
            List<int[]> result = new List<int[]>();
            for (int i = 0; i < triangles.Length; i += 3)
            {
                List<int> t = new List<int> { triangles[i], triangles[i + 1], triangles[i + 2] };
                t.Sort();
                result.Add(t.ToArray());
            }
            return result;
        }
    }
}