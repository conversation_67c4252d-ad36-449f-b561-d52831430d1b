{"name": "Unity.XR.Interaction.Toolkit.Samples.Hands", "rootNamespace": "", "references": ["Unity.InputSystem", "Unity.Mathematics", "Unity.XR.CoreUtils", "Unity.XR.Hands", "Unity.XR.Interaction.Toolkit"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.xr.hands", "expression": "1.1.0", "define": "XR_HANDS_1_1_OR_NEWER"}, {"name": "com.unity.xr.hands", "expression": "1.2.1", "define": "XR_HANDS_1_2_OR_NEWER"}, {"name": "com.unity.xr.hands", "expression": "1.5.0-pre.3", "define": "XR_HANDS_1_5_OR_NEWER"}], "noEngineReferences": false}