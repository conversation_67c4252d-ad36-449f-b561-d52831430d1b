{"name": "Unity.XR.Hands.Samples.VisualizerSample", "rootNamespace": "", "references": ["GUID:75469ad4d38634e559750d17036d5f7c", "GUID:dc960734dc080426fa6612f1c5fe95f3", "GUID:ce522b6ed64c8be4c989a1d26d0e3275", "GUID:4847341ff46394e83bb78fbd0652937e"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.xr.openxr", "expression": "1.0.0", "define": "OPENXR_AVAILABLE"}, {"name": "com.unity.xr.hands", "expression": "1.6.0-pre.1", "define": "XRHANDS_1_6_0"}], "noEngineReferences": false}